import Loader from "@/components/Loader";
import { useNavigate } from "@tanstack/react-router";


import { useFolderContext } from "@/contexts/FolderContext";
import { useEffect } from "react";

export default function Pages() {
  const navigate = useNavigate();
  const { selectedFolderId, folders, isLoading: foldersLoading } = useFolderContext();

  // Comprehensive logging for debugging
  useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Pages - Component state:`, {
      foldersLoading,
      selectedFolderId,
      foldersCount: folders?.length,
      foldersData: folders?.map(f => ({ id: f.folder_id, name: f.name })),
      shouldRedirect: !foldersLoading && folders && folders.length > 0
    });
  });

  // Use useEffect to handle navigation to avoid "setState during render" warning
  useEffect(() => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Pages - Navigation useEffect triggered:`, {
      foldersLoading,
      foldersCount: folders?.length,
      selectedFolderId,
      shouldNavigate: !foldersLoading && folders && folders.length > 0
    });

    if (!foldersLoading && folders && folders.length > 0) {
      const targetFolder = selectedFolderId || folders[0].folder_id;
      console.log(`[${timestamp}] Pages - Navigating to folder:`, { targetFolder });
      navigate({ to: "/pages/$folderId", params: { folderId: targetFolder }, replace: true });
    }
  }, [foldersLoading, folders, selectedFolderId, navigate]);

  if (foldersLoading) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Pages - Showing loader (folders loading)`);
    return <Loader />;
  }

  // If no folders exist, show empty state
  if (!foldersLoading && (!folders || folders.length === 0)) {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] Pages - Showing no folders state`);
    return (
      <div className="mx-4 my-4 h-full w-full">
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-center">
            <span className="text-sm text-text-secondary">No folders found. Please create a folder first.</span>
          </div>
        </div>
      </div>
    );
  }

  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] Pages - Showing fallback loader (waiting for navigation)`);
  return <Loader />;
}
